import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Zap,
  Plus,
  Settings,
  Calendar,
  Video,
  Webhook,
  Key,
  CheckCircle,
  AlertTriangle,
  ExternalLink
} from "lucide-react"
import { getIntegrations, toggleIntegration, configureIntegration, testIntegration } from "@/api/integrations"
import { toast } from "@/hooks/useToast"
import { useLanguage } from "@/contexts/LanguageContext"

interface Integration {
  _id: string
  name: string
  description: string
  category: string
  status: 'connected' | 'disconnected' | 'error'
  icon: string
  provider: string
  lastSync: string
  config: Record<string, any>
}

export function Integrations() {
  const [integrations, setIntegrations] = useState<Integration[]>([])
  const [loading, setLoading] = useState(true)
  const [configDialogOpen, setConfigDialogOpen] = useState(false)
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null)
  const { t } = useLanguage()

  useEffect(() => {
    fetchIntegrations()
  }, [])

  const fetchIntegrations = async () => {
    try {
      console.log('Fetching integrations list')
      const data = await getIntegrations()
      setIntegrations(data.integrations)
    } catch (error) {
      console.error('Error fetching integrations:', error)
      toast({
        title: "Error",
        description: "Failed to load integrations",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleToggleIntegration = async (integrationId: string, enabled: boolean) => {
    try {
      console.log('Toggling integration:', integrationId, enabled)
      await toggleIntegration(integrationId, enabled)
      toast({
        title: "Success",
        description: `Integration ${enabled ? 'connected' : 'disconnected'} successfully`,
      })
      fetchIntegrations()
    } catch (error) {
      console.error('Error toggling integration:', error)
      toast({
        title: "Error",
        description: "Failed to toggle integration",
        variant: "destructive",
      })
    }
  }

  const handleTestIntegration = async (integrationId: string) => {
    try {
      console.log('Testing integration:', integrationId)
      await testIntegration(integrationId)
      toast({
        title: "Success",
        description: "Integration test completed successfully",
      })
    } catch (error) {
      console.error('Error testing integration:', error)
      toast({
        title: "Error",
        description: "Integration test failed",
        variant: "destructive",
      })
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">{t('connected')}</Badge>
      case 'disconnected':
        return <Badge variant="secondary">{t('disconnected')}</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">{t('error')}</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getIntegrationIcon = (provider: string) => {
    switch (provider) {
      case 'google':
        return <Calendar className="h-8 w-8 text-blue-600" />
      case 'zoom':
        return <Video className="h-8 w-8 text-blue-500" />
      case 'webex':
        return <Video className="h-8 w-8 text-green-600" />
      case 'webhook':
        return <Webhook className="h-8 w-8 text-purple-600" />
      default:
        return <Zap className="h-8 w-8 text-slate-600" />
    }
  }

  const hasWebhooks = integrations.some(i => i.category === 'webhook')

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent dark:from-slate-100 dark:to-slate-400">
            {t('integrations')}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {t('connectExternalServices')}
          </p>
        </div>
        <Button className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600">
          <Plus className="mr-2 h-4 w-4" />
          {t('addIntegration')}
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">
              {t('totalIntegrations')}
            </CardTitle>
            <Zap className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
              {integrations.length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-200 dark:border-green-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">
              {t('connected')}
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900 dark:text-green-100">
              {(integrations || []).filter(i => i.status === 'connected').length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/20 dark:to-pink-950/20 border-red-200 dark:border-red-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-700 dark:text-red-300">
              {t('errors')}
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900 dark:text-red-100">
              {(integrations || []).filter(i => i.status === 'error').length}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20 border-purple-200 dark:border-purple-800">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">
              {t('categories')}
            </CardTitle>
            <Key className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
              {new Set(integrations.map(i => i.category)).size}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="all" className="space-y-6">
        <TabsList>
          <TabsTrigger value="all">{t('allIntegrations')}</TabsTrigger>
          <TabsTrigger value="calendar">{t('calendar')}</TabsTrigger>
          <TabsTrigger value="communication">{t('communication')}</TabsTrigger>
          {hasWebhooks && <TabsTrigger value="webhooks">{t('webhooks')}</TabsTrigger>}
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {loading ? (
              [...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 bg-slate-200 rounded"></div>
                      <div>
                        <div className="h-4 bg-slate-200 rounded w-24 mb-2"></div>
                        <div className="h-3 bg-slate-200 rounded w-16"></div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-slate-200 rounded"></div>
                      <div className="h-3 bg-slate-200 rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              integrations.map((integration) => (
                <Card key={integration._id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getIntegrationIcon(integration.provider)}
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                          <CardDescription className="capitalize">{integration.category}</CardDescription>
                        </div>
                      </div>
                      <Switch
                        checked={integration.status === 'connected'}
                        onCheckedChange={(checked) => handleToggleIntegration(integration._id, checked)}
                      />
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      {integration.description}
                    </p>

                    <div className="flex items-center justify-between">
                      {getStatusBadge(integration.status)}
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedIntegration(integration)
                            setConfigDialogOpen(true)
                          }}
                        >
                          <Settings className="h-4 w-4 mr-1" />
                          {t('configure')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleTestIntegration(integration._id)}
                        >
                          {t('test')}
                        </Button>
                      </div>
                    </div>

                    {integration.lastSync && (
                      <div className="text-xs text-slate-500">
                        {t('lastSync')}: {new Date(integration.lastSync).toLocaleString()}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="calendar">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {integrations
              .filter(i => i?.category === 'calendar')
              .map((integration) => (
                <Card key={integration._id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getIntegrationIcon(integration.provider)}
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                          <CardDescription>{t('calendarIntegration')}</CardDescription>
                        </div>
                      </div>
                      <Switch
                        checked={integration.status === 'connected'}
                        onCheckedChange={(checked) => handleToggleIntegration(integration._id, checked)}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                      {integration.description}
                    </p>
                    <div className="flex items-center justify-between">
                      {getStatusBadge(integration.status)}
                      <Button variant="outline" size="sm">
                        <ExternalLink className="h-4 w-4 mr-1" />
                        {t('openCalendar')}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </TabsContent>

        <TabsContent value="communication">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {integrations
              .filter(i => i?.category === 'communication')
              .map((integration) => (
                <Card key={integration._id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getIntegrationIcon(integration.provider)}
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                          <CardDescription>{t('videoConferencing')}</CardDescription>
                        </div>
                      </div>
                      <Switch
                        checked={integration.status === 'connected'}
                        onCheckedChange={(checked) => handleToggleIntegration(integration._id, checked)}
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                      {integration.description}
                    </p>
                    <div className="flex items-center justify-between">
                      {getStatusBadge(integration.status)}
                      <Button variant="outline" size="sm">
                        <Video className="h-4 w-4 mr-1" />
                        {t('startMeeting')}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </TabsContent>

        {hasWebhooks && (
          <TabsContent value="webhooks">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Webhook className="h-5 w-5" />
                  {t('webhookEndpoints')}
                </CardTitle>
                <CardDescription>
                  {t('configureWebhookEndpoints')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {integrations
                    .filter(i => i?.category === 'webhook')
                    .map((integration) => (
                      <div key={integration._id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Webhook className="h-6 w-6 text-purple-600" />
                          <div>
                            <div className="font-medium">{integration.name}</div>
                            <div className="text-sm text-slate-600 dark:text-slate-400">
                              {integration.description}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusBadge(integration.status)}
                          <Button variant="outline" size="sm">
                            <Settings className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>

      <Dialog open={configDialogOpen} onOpenChange={setConfigDialogOpen}>
        <DialogContent className="bg-white dark:bg-slate-900 max-w-md">
          <DialogHeader>
            <DialogTitle>{t('configure')} {selectedIntegration?.name}</DialogTitle>
            <DialogDescription>
              Update integration settings and authentication
            </DialogDescription>
          </DialogHeader>
          {selectedIntegration && (
            <IntegrationConfigForm
              integration={selectedIntegration}
              onClose={() => setConfigDialogOpen(false)}
              onSuccess={fetchIntegrations}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

function IntegrationConfigForm({ integration, onClose, onSuccess }: {
  integration: Integration
  onClose: () => void
  onSuccess: () => void
}) {
  const [config, setConfig] = useState(integration.config || {})
  const [saving, setSaving] = useState(false)
  const { t } = useLanguage()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    try {
      console.log('Configuring integration:', integration._id, config)
      await configureIntegration(integration._id, config)
      toast({
        title: "Success",
        description: "Integration configured successfully",
      })
      onSuccess()
      onClose()
    } catch (error) {
      console.error('Error configuring integration:', error)
      toast({
        title: "Error",
        description: "Failed to configure integration",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="apiKey">{t('apiKey')}</Label>
        <Input
          id="apiKey"
          type="password"
          value={config.apiKey || ''}
          onChange={(e) => setConfig({ ...config, apiKey: e.target.value })}
          placeholder={t('enterApiKey')}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="endpoint">{t('endpointUrl')}</Label>
        <Input
          id="endpoint"
          value={config.endpoint || ''}
          onChange={(e) => setConfig({ ...config, endpoint: e.target.value })}
          placeholder="https://api.example.com"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={config.description || ''}
          onChange={(e) => setConfig({ ...config, description: e.target.value })}
          rows={3}
        />
      </div>
      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          {t('cancel')}
        </Button>
        <Button type="submit" disabled={saving}>
          {saving ? t('saving') : t('saveConfiguration')}
        </Button>
      </div>
    </form>
  )
}