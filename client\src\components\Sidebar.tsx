import { NavLink } from "react-router-dom"
import { useState, useEffect } from "react"
import {
  LayoutDashboard,
  Users,
  Shield,
  Package,
  FileText,
  Zap,
  Settings,
  BarChart3,
  Calendar,
  Activity,
  Search
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "./ui/input"
import { Badge } from "./ui/badge"
import { useLanguage } from "@/contexts/LanguageContext"
import { getNavigationModules } from "@/api/modules"

interface SidebarProps {
  open: boolean
}

interface NavigationModule {
  _id: string
  name: string
  icon: string
  path: string
  order: number
}

interface NavigationItem {
  name: string
  href: string
  icon: any
  badge?: string
  isNew?: boolean
  order: number
}

export function Sidebar({ open }: SidebarProps) {
  const { t, language } = useLanguage()
  const isRTL = language === 'ar'
  const [navigationModules, setNavigationModules] = useState<NavigationModule[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    const fetchNavigationModules = async () => {
      try {
        const data = await getNavigationModules()
        setNavigationModules(data.modules)
      } catch (error) {
        console.error('Error fetching navigation modules:', error)
      }
    }

    fetchNavigationModules()
  }, [])

  const getIconComponent = (iconName: string) => {
    const iconMap: Record<string, any> = {
      LayoutDashboard,
      Users,
      Shield,
      Package,
      FileText,
      Zap,
      Settings,
      BarChart3,
      Calendar,
      Activity
    }
    return iconMap[iconName] || Package
  }

  // All navigation items in a flat structure
  const allNavigationItems: NavigationItem[] = [
    {
      name: t('dashboard'),
      href: '/',
      icon: LayoutDashboard,
      badge: "3",
      order: 1
    },
    {
      name: t('users'),
      href: '/users',
      icon: Users,
      badge: "12",
      order: 2
    },
    {
      name: t('roles'),
      href: '/role-management',
      icon: Shield,
      badge: "4",
      order: 3
    },
    {
      name: t('modules'),
      href: '/modules',
      icon: Package,
      isNew: true,
      order: 4
    },
    // Add dynamic modules
    ...navigationModules.map(module => ({
      name: module.name,
      href: module.path,
      icon: getIconComponent(module.icon),
      isNew: true,
      order: 10 + module.order
    })),
    {
      name: t('audit'),
      href: '/audit',
      icon: FileText,
      order: 20
    },
    {
      name: t('integrations'),
      href: '/integrations',
      icon: Zap,
      badge: "2",
      order: 21
    },
    {
      name: t('settings'),
      href: '/settings',
      icon: Settings,
      order: 22
    }
  ]

  // Sort by order and filter by search term
  const filteredItems = allNavigationItems
    .sort((a, b) => a.order - b.order)
    .filter(item =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase())
    )

  const renderNavigationItem = (item: NavigationItem) => (
    <NavLink
      key={item.href}
      to={item.href}
      className={({ isActive }) =>
        cn(
          "group flex items-center gap-3 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 relative overflow-hidden",
          "hover:bg-gradient-to-r hover:from-blue-50/80 hover:via-indigo-50/60 hover:to-purple-50/40 dark:hover:from-blue-950/30 dark:hover:via-indigo-950/20 dark:hover:to-purple-950/10",
          "hover:shadow-sm hover:shadow-blue-200/20 dark:hover:shadow-blue-900/10 hover:scale-[1.02] active:scale-[0.98]",
          "hover:border hover:border-blue-200/30 dark:hover:border-blue-800/30",
          isRTL ? "flex-row-reverse text-right" : "text-left",
          isActive
            ? "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-600 text-white shadow-lg shadow-blue-500/25 dark:shadow-blue-900/30 border border-blue-400/30"
            : "text-slate-700 dark:text-slate-300"
        )
      }
    >
      {({ isActive }) => (
        <>
          <div className={cn(
            "p-1.5 rounded-xl transition-all duration-300 flex-shrink-0",
            isActive
              ? "bg-white/20 shadow-sm"
              : "bg-gradient-to-br from-slate-100 to-slate-50 dark:from-slate-800 dark:to-slate-700 group-hover:bg-gradient-to-br group-hover:from-white group-hover:to-slate-50 dark:group-hover:from-slate-700 dark:group-hover:to-slate-600 group-hover:shadow-sm"
          )}>
            <item.icon className={cn(
              "h-4 w-4 transition-all duration-300",
              isActive ? "text-white drop-shadow-sm" : "text-slate-600 dark:text-slate-400 group-hover:text-blue-600 dark:group-hover:text-blue-400"
            )} />
          </div>

          {open && (
            <>
              <span className={cn(
                "flex-1 truncate",
                isRTL ? "text-right" : "text-left"
              )}>{item.name}</span>
              <div className={cn(
                "flex items-center gap-2 flex-shrink-0",
                isRTL ? "flex-row-reverse" : ""
              )}>
                {item.isNew && (
                  <Badge className={cn(
                    "bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs px-1.5 py-0.5 rounded-full shadow-sm",
                    isRTL ? "ml-1" : "mr-1"
                  )}>
                    {isRTL ? "جديد" : "New"}
                  </Badge>
                )}
                {item.badge && (
                  <Badge
                    variant={isActive ? "secondary" : "outline"}
                    className={cn(
                      "text-xs px-1.5 py-0.5 rounded-full transition-all duration-200",
                      isActive
                        ? "bg-white/20 text-white border-white/30 shadow-sm"
                        : "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-600 border-blue-200/50 dark:from-blue-950/30 dark:to-indigo-950/20 dark:text-blue-400 dark:border-blue-800/30"
                    )}
                  >
                    {item.badge}
                  </Badge>
                )}
              </div>
            </>
          )}

          {/* Hover effect overlay */}
          <div className={cn(
            "absolute inset-0 bg-gradient-to-r from-blue-500/5 via-indigo-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl",
            isActive && "opacity-0"
          )} />
        </>
      )}
    </NavLink>
  )

  return (
    <aside className={cn(
      "fixed top-16 z-30 h-[calc(100vh-4rem)] bg-white/90 backdrop-blur-xl border-slate-200/50 dark:bg-slate-900/90 dark:border-slate-700/50 transition-all duration-300 shadow-xl shadow-slate-200/10 dark:shadow-slate-800/20",
      isRTL ? "right-0 border-l" : "left-0 border-r",
      open ? "w-80" : "w-16"
    )}>
      <div className="flex flex-col h-full">
        {/* Header */}
        {open && (
          <div className={cn(
            "p-4 border-b border-slate-200/50 dark:border-slate-700/50",
            isRTL ? "text-right" : "text-left"
          )}>
            <div className="space-y-3">
              {/* Search */}
              <div className="relative">
                <Search className={cn(
                  "absolute top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400",
                  isRTL ? "right-3" : "left-3"
                )} />
                <Input
                  placeholder={isRTL ? "البحث في التنقل..." : "Search navigation..."}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={cn(
                    "h-9 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-700 border-slate-200/50 dark:border-slate-600/50 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 focus:shadow-lg focus:border-blue-300 dark:focus:border-blue-600",
                    isRTL ? "pr-10 text-right" : "pl-10 text-left"
                  )}
                  dir={isRTL ? "rtl" : "ltr"}
                />
              </div>
            </div>
          </div>
        )}

        {/* Navigation Content */}
        <div className="flex-1 overflow-y-auto p-3 space-y-1">
          {filteredItems.map(renderNavigationItem)}
        </div>

        {/* Footer */}
        <div className={cn(
          "p-4 border-t border-slate-200/50 dark:border-slate-700/50",
          !open && "px-2"
        )}>
          {open ? (
            <div className="space-y-2">
              <div className={cn(
                "flex items-center gap-3 p-2 rounded-xl bg-gradient-to-r from-slate-50 via-slate-25 to-blue-50/30 dark:from-slate-800 dark:via-slate-750 dark:to-slate-700 shadow-sm border border-slate-200/30 dark:border-slate-600/30",
                isRTL ? "flex-row-reverse text-right" : "text-left"
              )}>
                <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                  <span className="text-white font-bold text-sm drop-shadow-sm">W</span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className={cn(
                    "text-sm font-semibold bg-gradient-to-r from-slate-900 via-slate-700 to-slate-600 dark:from-slate-100 dark:via-slate-200 dark:to-slate-300 bg-clip-text text-transparent truncate",
                    isRTL ? "text-right" : "text-left"
                  )}>
                    {isRTL ? "ويب فيو إنتربرايز" : "WebVue Enterprise"}
                  </p>
                  <p className={cn(
                    "text-xs text-slate-500 dark:text-slate-400",
                    isRTL ? "text-right" : "text-left"
                  )}>
                    {isRTL ? "الإصدار 2.1.0 • متصل" : "v2.1.0 • Online"}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex justify-center">
              <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-sm drop-shadow-sm">W</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </aside>
  )
}